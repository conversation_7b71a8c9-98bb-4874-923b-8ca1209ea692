<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 联网权限 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 访问网络状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 读取外部存储 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <!-- 写入外部存储 -->
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <!-- API 33+ 照片读取权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <!-- API 33+ 音频读取权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <!-- API 33+ 视频读取权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 录音权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <!-- 修改音频设置权限 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <!-- Android 13+ 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.DETECT_SCREEN_CAPTURE" />
    <!-- 安装未知来源应用权限 (Android 8.0+) -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <!-- iBeancon 功能所需权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!-- 个推电子围栏功能所需权限 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <application
        android:name=".ZmlApp"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/app_logo"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@drawable/app_logo"
        android:supportsRtl="true"
        android:theme="@style/Theme.ZmlChat"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!--个推SDK的appid 重要！必须补充-->
        <meta-data
            android:name="PUSH_APPID"
            android:value="Rwe9P88JkH6woqlFKjphe2" />

        <activity
            android:name=".ui.activitys.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.ZmlChat.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activitys.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.ZmlChat" />

        <activity
            android:name=".ui.activitys.RegisterActivity"
            android:exported="false"
            android:theme="@style/Theme.ZmlChat" />
        <activity
            android:name=".ui.activitys.MainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.ZmlChat"
            android:windowSoftInputMode="adjustResize" />
        <activity android:name="com.zml.chat.ui.activitys.BillActivity" />
        <activity android:name="com.zml.chat.ui.activitys.ThridWebActivity" />

        <service android:name=".service.MyGTService" />
        <service
            android:name=".service.MyPushService"
            android:exported="false"
            android:label="PushService"
            android:process=":pushservice" />
        <!-- FileProvider for camera and file access -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>
    <queries>
        <intent>
            <action android:name="com.getui.sdk.action" />
        </intent>
    </queries>
</manifest>