package com.zml.chat

import android.app.Application
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import com.hjq.http.EasyConfig
import com.igexin.sdk.IUserLoggerInterface
import com.igexin.sdk.PushManager
import com.zml.chat.api.RequestHandler
import okhttp3.OkHttpClient


/**
 * 应用Application类
 */
class ZmlApp : Application() {
    
    companion object {
        lateinit var instance: ZmlApp
            private set
    }
    
    val dataStore: DataStore<Preferences> by preferencesDataStore(name = "zml_chat_prefs")
    
    override fun onCreate() {
        super.onCreate()
        
        instance = this
        
        // 初始化工具类
        Utils.init(this)

        //
        val okHttpClient = OkHttpClient.Builder().build()

        EasyConfig.with(okHttpClient) // 是否打印日志
            .setLogEnabled(BuildConfig.DEBUG) // 设置服务器配置（必须设置）
            .setServer(Constants.Api.BASE_URL) // 设置请求处理策略（必须设置）
            .setHandler(RequestHandler(this)) // 设置请求重试次数
            .setRetryCount(3) // 添加全局请求参数
            //.addParam("token", "6666666")
            // 添加全局请求头
            .addHeader("accessToken", SPUtils.getInstance().getString("token"))
            // 启用配置
            .into()
    }
} 