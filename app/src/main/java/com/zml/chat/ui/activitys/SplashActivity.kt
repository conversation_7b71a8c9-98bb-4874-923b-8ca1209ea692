package com.zml.chat.ui.activitys

import android.app.AlertDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.PathUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.ToastUtils
import com.hjq.http.EasyHttp
import com.hjq.http.config.IRequestApi
import com.hjq.http.listener.OnDownloadListener
import com.hjq.http.listener.OnHttpListener
import com.hjq.http.model.HttpMethod
import com.igexin.sdk.PushManager
import com.zml.chat.Constants
import com.zml.chat.R
import com.zml.chat.api.HttpData
import com.zml.chat.utils.DialogUtils
import org.json.JSONObject
import java.io.File

class SplashActivity : ComponentActivity() {

    companion object {
        private const val PREF_FIRST_LAUNCH = "first_launch"
    }

    private var downloadDialog: AlertDialog? = null
    private var progressBar: ProgressBar? = null
    private var tvProgressText: TextView? = null
    private var tvStatus: TextView? = null
    private var downloadUrl: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 确保启动画面显示至少800毫秒
        Handler(Looper.getMainLooper()).postDelayed({
            checkFirstLaunchAndProceed()
        }, 800)
    }

    /**
     * 检查是否第一次启动并处理相应逻辑
     */
    private fun checkFirstLaunchAndProceed() {
        checkUpDate {
            val isFirstLaunch = SPUtils.getInstance().getBoolean(PREF_FIRST_LAUNCH, true)

            if (isFirstLaunch) {
                // 第一次启动，显示隐私政策弹窗
                showPrivacyPolicyDialog()
            } else {
                // 不是第一次启动，直接进入主流程
                proceedToMain()
            }
        }
    }

    /**
     * 检查更新
     */
    private fun checkUpDate(final: () -> Unit) {
        EasyHttp.get(this)
            .api(Constants.Api.CHECK_UPDATE + "?version=" + AppUtils.getAppVersionName())
            .request(object : OnHttpListener<HttpData<JSONObject>> {
                override fun onHttpSuccess(result: HttpData<JSONObject>) {
                    val url = result.message
                    if (!StringUtils.isEmpty(url)) {
                        // 有更新，显示强制更新弹窗
                        showForceUpdateDialog(url)
                    } else {
                        // 没有更新，继续正常流程
                        final()
                    }
                }

                override fun onHttpFail(throwable: Throwable) {
                    // 网络请求失败，继续正常流程
                    final()
                }
            })
    }

    /**
     * 显示强制更新弹窗
     */
    private fun showForceUpdateDialog(downloadUrl: String) {
        DialogUtils.showInfoDialog(
            context = this,
            title = "发现新版本",
            message = "检测到新版本可用，为了获得更好的使用体验，请立即更新到最新版本。",
            confirmText = "立即更新",
            cancelable = false, // 不能取消
            onConfirm = {
                // 下载链接：downloadUrl
                downloadApp(downloadUrl)
            }
        )
    }

    /**
     * 下载应用更新包
     */
    private fun downloadApp(downloadUrl: String) {
        this.downloadUrl = downloadUrl
        startDownload()
    }

    /**
     * 开始下载
     */
    private fun startDownload() {
        showDownloadDialog()
        val fileName = "app_update_${System.currentTimeMillis()}.apk"
        val downloadDir = File(PathUtils.getExternalAppCachePath(), fileName)

        EasyHttp.download(this)
            .method(HttpMethod.GET)
            .file(downloadDir)
            .url(downloadUrl)
            // 设置断点续传（默认不开启）
            .resumableTransfer(true)
            .listener(object : OnDownloadListener {

                override fun onDownloadStart(file: File) {
                    runOnUiThread {
                        tvStatus?.text = "开始下载..."
                        progressBar?.progress = 0
                        tvProgressText?.text = "0%"
                    }
                }

                override fun onDownloadProgressChange(file: File, progress: Int) {
                    runOnUiThread {
                        progressBar?.progress = progress
                        tvProgressText?.text = "${progress}%"
                        tvStatus?.text = "正在下载... ${progress}%"
                    }
                }

                override fun onDownloadSuccess(file: File) {
                    runOnUiThread {
                        tvStatus?.text = "下载完成"
                        dismissDownloadDialog()
                        ToastUtils.showShort("下载完成：${file.path}")
                        AppUtils.installApp(file)
                    }
                }

                override fun onDownloadFail(file: File, throwable: Throwable) {
                    runOnUiThread {
                        dismissDownloadDialog()
                        ToastUtils.showShort("下载失败：${throwable.message}")
                        file.delete()

                        // 下载失败后显示重试对话框
                        showDownloadFailDialog()
                    }
                }

                override fun onDownloadEnd(file: File) {
                    // 下载结束（无论成功失败）
                }

            }).start()
    }

        /**
     * 显示下载进度弹窗
     */
    private fun showDownloadDialog() {
        val builder = AlertDialog.Builder(this)
        val view = LayoutInflater.from(this).inflate(R.layout.dialog_download_progress, null)
        
        progressBar = view.findViewById(R.id.progress_bar)
        tvProgressText = view.findViewById(R.id.tv_progress_text)
        tvStatus = view.findViewById(R.id.tv_status)
        
        // 添加速度和大小文本视图
        val tvSpeed = view.findViewById<TextView>(R.id.tv_speed)
        val tvSize = view.findViewById<TextView>(R.id.tv_size)
        
        builder.setView(view)
        builder.setCancelable(false) // 不允许取消
        
        downloadDialog = builder.create()
        
        // 先显示弹窗
        downloadDialog?.show()
        
        // 设置弹窗背景透明和尺寸
        downloadDialog?.window?.let { window ->
            window.setBackgroundDrawableResource(android.R.color.transparent)
            
            // 获取屏幕尺寸
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            
            // 设置弹窗宽度为屏幕宽度的80%，最大320dp
            val maxWidth = (320 * displayMetrics.density).toInt()
            val dialogWidth = minOf(screenWidth * 0.8f, maxWidth.toFloat()).toInt()
            
            // 设置窗口尺寸
            val layoutParams = window.attributes
            layoutParams.width = dialogWidth
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
            window.attributes = layoutParams
        }
    }

    /**
     * 关闭下载进度弹窗
     */
    private fun dismissDownloadDialog() {
        downloadDialog?.dismiss()
        downloadDialog = null
    }

    /**
     * 显示下载失败重试弹窗
     */
    private fun showDownloadFailDialog() {
        DialogUtils.showConfirmDialog(
            context = this,
            title = "下载失败",
            message = "网络连接异常，下载失败。请检查网络后重试。",
            confirmText = "重试",
            cancelText = "稍后再试",
            cancelable = false,
            onConfirm = {
                // 重试下载
                startDownload()
            },
            onCancel = {
                // 用户选择稍后再试，关闭应用
                finishAffinity()
            }
        )
    }

    /**
     * 显示隐私政策同意弹窗
     */
    private fun showPrivacyPolicyDialog() {
        val message =
            "欢迎使用我们的应用！在使用前，请阅读并同意我们的《隐私政策》和《用户协议》，了解我们如何收集、使用和保护您的个人信息。"

        // 定义可点击的文字和对应的操作
        val clickableTexts = listOf(
            "《隐私政策》" to {
                // 点击隐私政策的处理
                ThridWebActivity.start(Constants.PRIVATE)
            },
            "《用户协议》" to {
                // 点击用户协议的处理
                ThridWebActivity.start(Constants.FUWU)
            }
        )

        DialogUtils.showConfirmDialogWithClickableText(
            context = this,
            title = "隐私政策和用户协议",
            message = message,
            clickableTexts = clickableTexts,
            confirmText = "同意并继续",
            cancelText = "不同意",
            cancelable = false, // 不允许点击外部取消
            onConfirm = {
                // 用户同意，标记非首次启动，继续主流程
                SPUtils.getInstance().put(PREF_FIRST_LAUNCH, false)
                proceedToMain()
            },
            onCancel = {
                // 用户拒绝，关闭应用
                finishAffinity() // 关闭整个应用
            }
        )
    }

    /**
     * 进入主流程（登录检查）
     */
    private fun proceedToMain() {
        val userName = SPUtils.getInstance().getString("userName")
        val password = SPUtils.getInstance().getString("password")
        PushManager.getInstance().preInit(this)
        PushManager.getInstance().initialize(this)
        if (!StringUtils.isEmpty(userName) && !StringUtils.isEmpty(password)) {
            RegisterActivity.login(
                this, userName, password
            ) {
                ActivityUtils.startActivity(LoginActivity::class.java)
                finish()
            }
        } else {
            ActivityUtils.startActivity(LoginActivity::class.java)
            finish()
        }
    }
}