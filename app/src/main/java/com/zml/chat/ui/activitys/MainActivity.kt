package com.zml.chat.ui.activitys

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.media.MediaRecorder
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.view.WindowManager
import android.webkit.ValueCallback
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedCallback
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ThreadUtils
import com.blankj.utilcode.util.ToastUtils
import com.hjq.http.EasyHttp
import com.hjq.http.listener.OnHttpListener
import com.igexin.sdk.IUserLoggerInterface
import com.igexin.sdk.PushManager
import com.zml.chat.Constants
import com.zml.chat.R
import com.zml.chat.api.HttpData
import com.zml.chat.model.LoginModel
import com.zml.chat.utils.ScreenObserverUtil
import com.zml.chat.widget.CustomWebView
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*


class MainActivity : ComponentActivity() {
    private lateinit var mWebView: CustomWebView
    private val REQUEST_FILE_CHOOSER = 1002
    private val REQUEST_CAMERA_PERMISSION = 1003
    private val REQUEST_AUDIO_PERMISSION = 1004
    private var filePathCallback: ValueCallback<Array<Uri>>? = null
    private var pendingFileChooserIntent: Intent? = null
    private var audioCallback: ValueCallback<String>? = null

    // 录音相关变量
    private var mediaRecorder: MediaRecorder? = null
    private var audioFile: File? = null
    private var isRecording = false

    // 使用Activity Result API注册文件选择结果处理器
    private val fileChooserResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        mWebView.handleFileChooserResult(result.resultCode, result.data)
    }

    // 使用Activity Result API注册权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        // 检查相机权限结果
        if (permissions.containsKey(Manifest.permission.CAMERA)) {
            // 处理相机权限结果
            if (permissions[Manifest.permission.CAMERA] == true) {
                // 相机权限已授予，继续处理文件选择
                pendingFileChooserIntent?.let {
                    fileChooserResultLauncher.launch(it)
                    pendingFileChooserIntent = null
                }
            } else {
                // 权限被拒绝，通知用户
                Toast.makeText(this, "需要相机权限才能拍照", Toast.LENGTH_SHORT).show()
                // 返回空结果给WebView
                filePathCallback?.onReceiveValue(arrayOf())
                filePathCallback = null
            }
        }
    }

    // 使用Activity Result API注册音频权限请求
    private val requestAudioPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.entries.all { it.value }
        if (allGranted) {
            // 权限已授予，开始录音
            LogUtils.d("音频权限已授予，开始录音")
            startRecordingAudio()
        } else {
            // 权限被拒绝，通知用户
            Toast.makeText(this, "需要录音权限才能使用语音功能", Toast.LENGTH_SHORT).show()
            // 返回错误给WebView
            audioCallback?.onReceiveValue("")
            audioCallback = null
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        PushManager.getInstance().preInit(this)
        PushManager.getInstance().initialize(this)
        PushManager.getInstance().setDebugLogger(this, object : IUserLoggerInterface {
            override fun log(s: String) {
                Log.i("PUSH_LOG", s)
            }
        })
        // 禁止截屏和录屏
        window.setFlags(
            WindowManager.LayoutParams.FLAG_SECURE,
            WindowManager.LayoutParams.FLAG_SECURE
        )
        ScreenObserverUtil.register(this, {
            traceMediaEvent(true)
        }, {
            traceMediaEvent(false)
        })

        ThreadUtils.runOnUiThreadDelayed({
            ActivityUtils.finishActivity(LoginActivity::class.java)
            ActivityUtils.finishActivity(RegisterActivity::class.java)
            ActivityUtils.finishActivity(SplashActivity::class.java)
        }, 500)

        // 恢复软键盘模式为SOFT_INPUT_ADJUST_RESIZE，但会通过自定义处理控制顶起的高度
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        setContentView(R.layout.activity_main)
        initScreen()

        // 自定义WindowInsets处理器，只应用部分键盘高度作为padding
        // 通过这种方式控制键盘顶起内容的程度
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(android.R.id.content)) { view, insets ->
            // 应用100%的键盘高度，精确顶起软键盘高度
            val adjustedPadding = 0
            // 应用调整后的padding
            view.setPadding(view.paddingLeft, view.paddingTop, view.paddingRight, adjustedPadding)
            insets
        }
    }

    private fun traceMediaEvent(isScreenShot: Boolean) {
        EasyHttp.post(this)
            .api(Constants.Api.ACTIONTYPE)
            .json((JSONObject().apply {
                put("actionType", if (isScreenShot) "screenshot" else "screenrecord")
            }).toString())
            .request(object : OnHttpListener<HttpData<String>> {
                override fun onHttpSuccess(result: HttpData<String>) {

                }

                override fun onHttpFail(throwable: Throwable) {

                }
            })
    }

    private fun initScreen() {
        val loginModel = SPUtils.getInstance().getString("loginInfo")

        enableEdgeToEdge()

        // 初始化CustomWebView
        mWebView = findViewById<CustomWebView>(R.id.webview)
        mWebView.initialize()

        // 设置权限监听器
        mWebView.setPermissionListener(object : CustomWebView.PermissionListener {
            override fun requestPermissions(
                permissions: Array<String>,
                requestCode: Int,
                permissionType: String
            ) {
                // 根据权限类型选择不同的请求处理器
                when (permissionType) {
                    "audio" -> {
                        // 录音权限请求
                        requestAudioPermissionLauncher.launch(permissions)
                    }

                    else -> {
                        // 其他权限请求(包括相机)
                        requestPermissionLauncher.launch(permissions)
                    }
                }
            }

            override fun onShowFileChooser(
                filePathCallback: ValueCallback<Array<Uri>>?,
                intent: Intent
            ) {
                // 保存回调
                <EMAIL> = filePathCallback

                // 检查intent是否包含相机操作
                val containsCameraAction = intent.hasExtra(Intent.EXTRA_INITIAL_INTENTS) &&
                        (intent.getParcelableArrayExtra(Intent.EXTRA_INITIAL_INTENTS) as? Array<Intent>)?.any {
                            it.action == MediaStore.ACTION_IMAGE_CAPTURE
                        } == true

                if (containsCameraAction && ContextCompat.checkSelfPermission(
                        this@MainActivity,
                        Manifest.permission.CAMERA
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    // 需要相机权限但未授予，保存intent并请求权限
                    LogUtils.d("需要相机权限，正在请求")
                    pendingFileChooserIntent = intent
                    requestPermissionLauncher.launch(arrayOf(Manifest.permission.CAMERA))
                } else {
                    // 已有权限或不需要相机权限，直接启动选择器
                    LogUtils.d("启动文件/图片选择器")
                    fileChooserResultLauncher.launch(intent)
                }
            }

            override fun onRecordAudio(callback: ValueCallback<String>) {
                // 保存回调
                audioCallback = callback

                // 检查录音相关权限
                val audioPermissions = arrayOf(
                    Manifest.permission.RECORD_AUDIO,
                    Manifest.permission.MODIFY_AUDIO_SETTINGS
                )

                if (hasPermissions(audioPermissions)) {
                    // 已有权限，开始录音
                    LogUtils.d("已有录音权限，开始录音")
                    startRecordingAudio()
                } else {
                    // 请求录音权限
                    LogUtils.d("请求录音权限")
                    requestAudioPermissionLauncher.launch(audioPermissions)
                }
            }
        })

        // 加载URL
//        mWebView.loadChatInterface("http://192.168.2.73:5173/#/",loginModel)
        mWebView.loadChatInterface("https://chatu.hfhcjfkj.com/#/", loginModel)

        // 注册物理返回键处理
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (!mWebView.handleBackPressed()) {
                    finish()
                }
            }
        })
    }

    // 添加抑制过时API的警告
    @Suppress("OVERRIDE_DEPRECATION", "DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        // 处理文件选择结果
        if (requestCode == REQUEST_FILE_CHOOSER) {
            mWebView.handleFileChooserResult(resultCode, data)
        }
    }

    override fun onResume() {
        super.onResume()
        // 让WebView获取焦点
        mWebView.requestFocus()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 停止录音，释放资源
        stopRecording()
        // 清理资源
        mWebView.onDestroy()
        ScreenObserverUtil.unRegister(this)
    }

    // 检查是否有指定的所有权限
    private fun hasPermissions(permissions: Array<String>): Boolean {
        return permissions.all {
            ContextCompat.checkSelfPermission(this, it) == PackageManager.PERMISSION_GRANTED
        }
    }

    // 创建录音文件
    @Throws(IOException::class)
    private fun createAudioFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val audioFileName = "AUDIO_$timeStamp"
        val storageDir = getExternalFilesDir(Environment.DIRECTORY_MUSIC)
        return File.createTempFile(
            audioFileName,
            ".mp3",
            storageDir
        )
    }

    // 开始录音实现
    private fun startRecordingAudio() {
        try {
            // 创建录音文件
            audioFile = createAudioFile()
            LogUtils.d("开始录音...文件路径: ${audioFile?.absolutePath}")

            // 初始化MediaRecorder
            mediaRecorder =
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                    MediaRecorder(this)
                } else {
                    @Suppress("DEPRECATION")
                    MediaRecorder()
                }

            mediaRecorder?.apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                setAudioEncodingBitRate(128000)
                setAudioSamplingRate(44100)
                setOutputFile(audioFile?.absolutePath)

                try {
                    prepare()
                    start()
                    isRecording = true

                    // 通知WebView录音已开始
                    audioCallback?.onReceiveValue(audioFile?.absolutePath ?: "")
                } catch (e: Exception) {
                    LogUtils.e("录音准备或开始失败", e)
                    stopRecording()
                    audioCallback?.onReceiveValue("")
                    audioCallback = null
                    Toast.makeText(
                        this@MainActivity,
                        "录音启动失败: ${e.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        } catch (e: Exception) {
            LogUtils.e("录音初始化失败", e)
            stopRecording()
            audioCallback?.onReceiveValue("")
            audioCallback = null
            Toast.makeText(this@MainActivity, "录音初始化失败: ${e.message}", Toast.LENGTH_SHORT)
                .show()
        }
    }

    // 停止录音
    private fun stopRecording() {
        if (isRecording) {
            try {
                mediaRecorder?.apply {
                    stop()
                    reset()
                    release()
                }
                isRecording = false
                LogUtils.d("录音停止，文件保存在: ${audioFile?.absolutePath}")
            } catch (e: Exception) {
                LogUtils.e("停止录音失败", e)
            } finally {
                mediaRecorder = null
            }
        }
    }
}