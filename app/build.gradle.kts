plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    kotlin("kapt")
}

android {
    signingConfigs {
        getByName("debug") {
            storeFile = file("../zml")
            storePassword = "zml2025_."
            keyAlias = "zml"
            keyPassword = "zml2025_."
        }
        create("release") {
            storeFile = file("../zml")
            storePassword = "zml2025_."
            keyAlias = "zml"
            keyPassword = "zml2025_."
        }
//        getByName("debug") {
//            storeFile = file("../jtgw")
//            storePassword = "qwer1234."
//            keyAlias = "jw"
//            keyPassword = "qwer1234."
//        }
//        create("release") {
//            storeFile = file("../jtgw")
//            storePassword = "qwer1234."
//            keyAlias = "jw"
//            keyPassword = "qwer1234."
//        }
    }
    namespace = "com.zml.chat"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.zml.chat"

        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        // NDK ABI过滤配置
        ndk {
            abiFilters.add("armeabi-v7a")
            abiFilters.add("arm64-v8a")
            abiFilters.add("x86")
            abiFilters.add("x86_64")
        }

        manifestPlaceholders.putAll(
            arrayOf(
                "GETUI_APPID" to "Rwe9P88JkH6woqlFKjphe2"
            )
        )
    }

    buildTypes {
        debug {
            signingConfig = signingConfigs.getByName("debug")
        }
        release {
            isMinifyEnabled = false
            signingConfig = signingConfigs.getByName("release")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        compose = true
    }

    lint {
        abortOnError = false
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation("androidx.compose.material:material-icons-core:1.5.4")
    implementation("androidx.compose.material:material-icons-extended:1.5.4")

    // SplashScreen API
    implementation("androidx.core:core-splashscreen:1.0.1")

    // Navigation组件
    implementation("androidx.navigation:navigation-compose:2.7.7")

    // 日历组件
    implementation("io.github.vanpra.compose-material-dialogs:datetime:0.9.0")

    // 图片加载库
    implementation("io.coil-kt:coil-compose:2.4.0")

    // DataStore
    implementation("androidx.datastore:datastore-preferences:1.0.0")

    // CardView
    implementation("androidx.cardview:cardview:1.0.0")

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
    implementation("com.github.getActivity:EasyHttp:13.0")
    implementation("com.squareup.okhttp3:okhttp:3.12.13")
    implementation("com.blankj:utilcodex:1.31.1")
    implementation("com.github.getActivity:GsonFactory:9.6")
    implementation("com.google.code.gson:gson:2.10.1")
    // Room数据库
    val roomVersion = "2.6.1"
    implementation("androidx.room:room-runtime:$roomVersion")
    implementation("androidx.room:room-ktx:$roomVersion")
    kapt("androidx.room:room-compiler:$roomVersion")

    // 添加java.time反向兼容支持
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
    implementation("com.tencent.bugly:crashreport:4.1.9.3")

    //
    implementation("com.getui:gtsdk:3.3.11.0")
    implementation("com.getui:gtc:3.2.18.0")
}